#!/usr/bin/env python3
"""
Simple concurrent test: 20 ingestion + 80 inference requests
Reports when each request gets a response (no timeouts)
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

# Server configuration
BASE_URL = "http://127.0.0.1:8000"
INGESTION_URL = f"{BASE_URL}/api/v1/ingest/text"
INFERENCE_URL = f"{BASE_URL}/api/v1/inference"

# Test configuration
TOTAL_REQUESTS = 100
INGESTION_REQUESTS = 20
INFERENCE_REQUESTS = 80

async def send_ingestion_request(session, request_id):
    """Send a single ingestion request"""
    payload = {
        "collection_name": f"test_collection_{request_id}",
        "source_identifier": f"source_{request_id}",
        "source_name": f"Test Source {request_id}",
        "text_content": f"This is test content for ingestion request {request_id}. The secret code is: apple_{request_id}."
    }
    
    start_time = time.time()
    try:
        async with session.post(INGESTION_URL, json=payload) as response:
            response_time = time.time() - start_time
            status = response.status
            if status == 200:
                data = await response.json()
                print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] INGESTION {request_id:3d}: SUCCESS in {response_time:.3f}s - ID: {data.get('ingestion_id', 'N/A')}")
            else:
                text = await response.text()
                print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] INGESTION {request_id:3d}: ERROR {status} in {response_time:.3f}s - {text[:100]}")
            return {"type": "ingestion", "id": request_id, "status": status, "time": response_time}
    except Exception as e:
        response_time = time.time() - start_time
        print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] INGESTION {request_id:3d}: EXCEPTION in {response_time:.3f}s - {str(e)[:100]}")
        return {"type": "ingestion", "id": request_id, "status": "exception", "time": response_time, "error": str(e)}

async def send_inference_request(session, request_id):
    """Send a single inference request"""
    payload = {
        "query": f"What is the secret code for request {request_id}?",
        "collection_name": f"test_collection_{request_id % INGESTION_REQUESTS + 1}",  # Use existing collections
        "config": {
            "max_new_tokens": 50
        },
        "language": "en"
    }
    
    start_time = time.time()
    try:
        async with session.post(INFERENCE_URL, json=payload) as response:
            status = response.status
            if status == 200:
                # For streaming responses, we just read the first chunk to confirm it started
                first_chunk = await response.content.read(100)
                response_time = time.time() - start_time
                print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] INFERENCE {request_id:3d}: SUCCESS in {response_time:.3f}s - Started streaming")
                
                # Continue reading the rest of the stream without blocking other requests
                asyncio.create_task(consume_stream(response, request_id, start_time))
            else:
                response_time = time.time() - start_time
                text = await response.text()
                print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] INFERENCE {request_id:3d}: ERROR {status} in {response_time:.3f}s - {text[:100]}")
            return {"type": "inference", "id": request_id, "status": status, "time": time.time() - start_time}
    except Exception as e:
        response_time = time.time() - start_time
        print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] INFERENCE {request_id:3d}: EXCEPTION in {response_time:.3f}s - {str(e)[:100]}")
        return {"type": "inference", "id": request_id, "status": "exception", "time": response_time, "error": str(e)}

async def consume_stream(response, request_id, start_time):
    """Consume the rest of a streaming response"""
    try:
        async for chunk in response.content.iter_chunked(1024):
            pass  # Just consume the stream
        total_time = time.time() - start_time
        print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] INFERENCE {request_id:3d}: COMPLETED streaming in {total_time:.3f}s total")
    except Exception as e:
        total_time = time.time() - start_time
        print(f"[{datetime.now().strftime('%H:%M:%S.%f')[:-3]}] INFERENCE {request_id:3d}: STREAM ERROR in {total_time:.3f}s - {str(e)[:50]}")

async def run_concurrent_test():
    """Run the concurrent test with 20 ingestion + 80 inference requests"""
    print(f"Starting concurrent test: {INGESTION_REQUESTS} ingestion + {INFERENCE_REQUESTS} inference = {TOTAL_REQUESTS} total requests")
    print(f"Target server: {BASE_URL}")
    print(f"Test started at: {datetime.now().strftime('%H:%M:%S')}")
    print("-" * 80)
    
    # Create session with no timeout
    timeout = aiohttp.ClientTimeout(total=None)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        
        # Create all tasks
        tasks = []
        
        # Add ingestion tasks
        for i in range(1, INGESTION_REQUESTS + 1):
            task = send_ingestion_request(session, i)
            tasks.append(task)
        
        # Add inference tasks
        for i in range(1, INFERENCE_REQUESTS + 1):
            task = send_inference_request(session, i)
            tasks.append(task)
        
        # Send all requests simultaneously
        print(f"Sending all {TOTAL_REQUESTS} requests simultaneously...")
        start_time = time.time()
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        print("-" * 80)
        print(f"All requests completed in {total_time:.3f}s")
        
        # Summary
        successful = sum(1 for r in results if isinstance(r, dict) and r.get('status') == 200)
        errors = sum(1 for r in results if isinstance(r, dict) and r.get('status') != 200)
        exceptions = sum(1 for r in results if not isinstance(r, dict))
        
        print(f"Results: {successful} successful, {errors} errors, {exceptions} exceptions")
        print(f"Success rate: {successful/TOTAL_REQUESTS*100:.1f}%")

if __name__ == "__main__":
    asyncio.run(run_concurrent_test())
